import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
import numpy as np
import os
from math import pi

# Device configuration
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
torch.backends.cudnn.benchmark = True

# Hyperparameters
n_grid = 32
batch_size = 32
learning_rate = 1e-4
num_epochs = 100
warmup_steps = 1000
max_lambda_pde = 1.0
scale = 20.0  # scale (u, f) together to O(1)

# Dataset for Poisson solutions (Periodic BC, sin-sin basis with 2π)
class PoissonDataset(Dataset):
    def __init__(self, size=200000, n=32, kmax=10, mmax=10, seed=42):
        self.size = size
        self.n = n
        self.h = 1.0 / n
        rng = np.random.default_rng(seed)
        self.grid = np.linspace(0, 1, self.n, endpoint=False, dtype=np.float32)
        self.X, self.Y = np.meshgrid(self.grid, self.grid, indexing="xy")
        self.rng = rng
        self.kmax = kmax
        self.mmax = mmax

    def __len__(self):
        return self.size

    def __getitem__(self, idx):
        # Random modes (avoid zero)
        k = self.rng.integers(1, self.kmax + 1)
        m = self.rng.integers(1, self.mmax + 1)
        # Periodic with sin(2π k x) sin(2π m y)
        f = np.sin(2 * k * pi * self.X) * np.sin(2 * m * pi * self.Y)
        u = -f / (4 * pi**2 * (k**2 + m**2))
        # Scale both u and f by the same factor
        f = (scale * f).astype(np.float32)
        u = (scale * u).astype(np.float32)
        return torch.from_numpy(f), torch.from_numpy(u)

# Simple UNet model (add t as a channel, use circular padding for periodic)
class SimpleUNet(nn.Module):
    def __init__(self, in_channels=3, out_channels=1):
        super(SimpleUNet, self).__init__()
        self.conv1 = nn.Conv2d(in_channels, 64, 3, padding=1, padding_mode='circular')
        self.conv2 = nn.Conv2d(64, 64, 3, padding=1, padding_mode='circular')
        self.down = nn.MaxPool2d(2)
        self.mid_conv1 = nn.Conv2d(64, 128, 3, padding=1, padding_mode='circular')
        self.mid_conv2 = nn.Conv2d(128, 128, 3, padding=1, padding_mode='circular')
        self.up = nn.ConvTranspose2d(128, 64, 2, stride=2)
        self.final_conv1 = nn.Conv2d(128, 64, 3, padding=1, padding_mode='circular')
        self.final_conv2 = nn.Conv2d(64, out_channels, 3, padding=1, padding_mode='circular')
        self.act = nn.SiLU()

    def forward(self, x):
        c1 = self.act(self.conv1(x))
        c2 = self.act(self.conv2(c1))
        d = self.down(c2)
        m1 = self.act(self.mid_conv1(d))
        m2 = self.act(self.mid_conv2(m1))
        u = self.up(m2)
        cat = torch.cat([u, c2], dim=1)
        f1 = self.act(self.final_conv1(cat))
        out = self.final_conv2(f1)
        return out

# Spectral Laplacian for periodic domain (exact for Fourier modes)
def spectral_laplacian(u):
    # u: [B, 1, H, W], domain is [0,1]×[0,1], periodic
    B, C, H, W = u.shape
    # frequencies in cycles per unit length (integers for d=1/n)
    kx = torch.fft.fftfreq(W, d=1.0/W).to(u.device).reshape(1, 1, 1, W)
    ky = torch.fft.fftfreq(H, d=1.0/H).to(u.device).reshape(1, 1, H, 1)
    k2 = (2 * torch.pi) ** 2 * (kx ** 2 + ky ** 2)  # (1,1,H,W)
    U = torch.fft.fft2(u)
    lapU = -k2 * U
    lapu = torch.fft.ifft2(lapU).real
    return lapu

def train():
    dataset = PoissonDataset(size=200000, n=n_grid)
    loader = DataLoader(dataset, batch_size=batch_size, shuffle=True,
                        num_workers=4, pin_memory=True, drop_last=True)

    # Sanity check for spectral Laplacian
    batch_f, batch_u = next(iter(loader))
    f_check = batch_f[0:1].unsqueeze(1).to(device)  # [1,1,n,n]
    u_check = batch_u[0:1].unsqueeze(1).to(device)
    lap_u_check = spectral_laplacian(u_check)
    diff = torch.abs(lap_u_check - f_check)
    print(f"Sanity check - Max diff: {diff.max().item():.4e}, Mean diff: {diff.mean().item():.4e}")

    model = SimpleUNet(in_channels=3, out_channels=1).to(device)
    optimizer = torch.optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=1e-4)

    scaler = torch.cuda.amp.GradScaler(enabled=(device.type == "cuda"))
    total_steps = 0

    for epoch in range(num_epochs):
        for batch_f, batch_u in loader:
            f = batch_f.unsqueeze(1).to(device, non_blocking=True)  # [b,1,n,n]
            x = batch_u.unsqueeze(1).to(device, non_blocking=True)  # [b,1,n,n]

            # Flow-matching (rectified flow): x_t = (1 - t) z + t x
            std_x = x.std(dim=(2,3), keepdim=True).clamp_min(1e-2)
            z = torch.randn_like(x) * std_x
            t = torch.rand((x.shape[0], 1, 1, 1), device=device) * (1 - 1e-5) + 1e-5
            x_t = (1 - t) * z + t * x
            t_chan = t.expand(-1, 1, n_grid, n_grid)  # broadcast t as a channel
            input_t = torch.cat([x_t, f, t_chan], dim=1)

            with torch.cuda.amp.autocast(enabled=(device.type == "cuda")):
                v_pred = model(input_t)
                v_target = x - z  # constant along the straight path
                loss_meanflow = torch.mean((v_pred - v_target) ** 2)

                # Lambda schedule: warm up then ramp
                if total_steps > warmup_steps:
                    ramp = (total_steps - warmup_steps) / (warmup_steps * 10)
                    lambda_pde = max_lambda_pde * float(min(1.0, ramp))
                else:
                    lambda_pde = 0.0

                if lambda_pde > 0:
                    # One-step generation at t=0 for PDE residual
                    t0 = torch.zeros_like(t)
                    t0_chan = t0.expand(-1, 1, n_grid, n_grid)
                    z_pde = torch.randn_like(x) * std_x
                    input_pde = torch.cat([z_pde, f, t0_chan], dim=1)
                    v_pde = model(input_pde)
                    u_gen = z_pde + v_pde  # one-step sample
                    u_gen = u_gen - u_gen.mean(dim=(2,3), keepdim=True)  # Zero mean projection
                    lap_u = spectral_laplacian(u_gen)
                    res = lap_u - f  # ∇²u - f = 0
                    loss_pde = torch.mean(res ** 2)
                else:
                    loss_pde = torch.tensor(0.0, device=device)

                loss = loss_meanflow + lambda_pde * loss_pde

            optimizer.zero_grad(set_to_none=True)
            scaler.scale(loss).backward()
            scaler.step(optimizer)
            scaler.update()

            total_steps += 1
            if total_steps % 100 == 0:
                print(f"Step {total_steps} | L_meanflow: {loss_meanflow.item():.4e} | "
                      f"L_pde: {float(loss_pde):.4e} | λ_pde: {lambda_pde:.3f}")

        # Save model periodically
        if (epoch + 1) % 10 == 0:
            os.makedirs("ckpts", exist_ok=True)
            torch.save(model.state_dict(), f"ckpts/model_epoch_{epoch+1}.pth")

def sample(model, f, n_samples=1):
    """
    model: trained SimpleUNet
    f: [1, 1, n, n] torch tensor on device
    return: [n_samples, 1, n, n]
    """
    model.eval()
    outs = []
    with torch.no_grad():
        for _ in range(n_samples):
            z = torch.randn_like(f)
            t0 = torch.zeros((f.shape[0], 1, 1, 1), device=f.device)
            t0_chan = t0.expand(-1, 1, f.shape[-2], f.shape[-1])
            inp = torch.cat([z, f, t0_chan], dim=1)
            v = model(inp)
            u = z + v
            outs.append(u)
    return torch.cat(outs, dim=0)

if __name__ == "__main__":
    train()