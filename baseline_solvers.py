import torch
import torch.nn as nn
import numpy as np
import time
from math import pi
from scipy.sparse import diags
from scipy.sparse.linalg import spsolve
import scipy.sparse as sp

class FFTSolver:
    """
    快速傅里叶变换求解器 - 对周期边界条件的 Poisson 方程精确求解
    理论上最快最准确，作为 ground truth 基线
    """
    def __init__(self, n_grid=32):
        self.n_grid = n_grid
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
    def solve(self, f):
        """
        求解 ∇²u = f，周期边界条件
        f: [batch_size, 1, n, n] 或 [n, n]
        返回: u 同样形状
        """
        if f.dim() == 2:
            f = f.unsqueeze(0).unsqueeze(0)
        elif f.dim() == 3:
            f = f.unsqueeze(1)
            
        B, C, H, W = f.shape
        
        # 构建频率网格
        kx = torch.fft.fftfreq(W, d=1.0/W).to(f.device).reshape(1, 1, 1, W)
        ky = torch.fft.fftfreq(H, d=1.0/H).to(f.device).reshape(1, 1, H, 1)
        k2 = (2 * torch.pi) ** 2 * (kx ** 2 + ky ** 2)
        
        # 避免除零（k=0 对应常数项，设为 0）
        k2[k2 == 0] = 1.0
        
        # FFT 求解
        F = torch.fft.fft2(f)
        U = -F / k2
        U[..., 0, 0] = 0  # 零均值约束
        u = torch.fft.ifft2(U).real
        
        return u

class FiniteDifferenceSolver:
    """
    有限差分法 + 共轭梯度求解器
    经典数值方法，适中的精度和速度
    """
    def __init__(self, n_grid=32, max_iter=1000, tol=1e-6):
        self.n_grid = n_grid
        self.h = 1.0 / n_grid
        self.max_iter = max_iter
        self.tol = tol
        self._build_laplacian_matrix()
        
    def _build_laplacian_matrix(self):
        """构建五点差分的拉普拉斯矩阵（周期边界）"""
        n = self.n_grid
        N = n * n
        h2 = self.h ** 2
        
        # 主对角线
        main_diag = -4 * np.ones(N) / h2
        
        # 上下邻居（y方向）
        upper_diag = np.ones(N-n) / h2
        lower_diag = np.ones(N-n) / h2
        
        # 左右邻居（x方向）
        left_diag = np.ones(N-1) / h2
        right_diag = np.ones(N-1) / h2
        
        # 处理周期边界条件
        # y方向周期
        y_periodic_upper = np.zeros(N)
        y_periodic_lower = np.zeros(N)
        for i in range(n):
            y_periodic_upper[i] = 1.0 / h2  # 顶部连接底部
            y_periodic_lower[N-n+i] = 1.0 / h2  # 底部连接顶部
            
        # x方向周期
        x_periodic = np.zeros(N)
        for i in range(n):
            left_diag[i*n-1] = 0  # 移除行末的连接
            right_diag[i*n] = 0   # 移除行首的连接
            x_periodic[i*n] = 1.0 / h2      # 行首连接行末
            x_periodic[(i+1)*n-1] = 1.0 / h2  # 行末连接行首
            
        # 构建稀疏矩阵
        diagonals = [
            y_periodic_lower,  # 底部周期
            lower_diag,        # 下邻居
            left_diag,         # 左邻居  
            main_diag,         # 主对角
            right_diag,        # 右邻居
            upper_diag,        # 上邻居
            y_periodic_upper   # 顶部周期
        ]
        offsets = [-(N-n), -n, -1, 0, 1, n, N-n]
        
        self.A = diags(diagonals, offsets, shape=(N, N), format='csr')
        
        # 处理奇异性：固定一个点的值为0（零均值约束）
        self.A[0, :] = 0
        self.A[0, 0] = 1
        
    def solve(self, f):
        """
        求解 ∇²u = f
        f: [batch_size, 1, n, n] 或 [n, n]
        """
        original_shape = f.shape
        if f.dim() == 2:
            f = f.unsqueeze(0).unsqueeze(0)
        elif f.dim() == 3:
            f = f.unsqueeze(1)
            
        B, C, H, W = f.shape
        solutions = []
        
        for b in range(B):
            f_flat = f[b, 0].cpu().numpy().flatten()
            f_flat[0] = 0  # 零均值约束
            
            # 使用稀疏求解器
            u_flat = spsolve(self.A, f_flat)
            u = torch.from_numpy(u_flat.reshape(H, W)).float()
            solutions.append(u)
            
        result = torch.stack(solutions).unsqueeze(1)
        
        # 恢复原始形状
        if len(original_shape) == 2:
            result = result.squeeze(0).squeeze(0)
        elif len(original_shape) == 3:
            result = result.squeeze(1)
            
        return result

class MultigridSolver:
    """
    简化的多重网格求解器
    使用 V-cycle，适合大规模问题
    """
    def __init__(self, n_grid=32, levels=3, pre_smooth=2, post_smooth=2):
        self.n_grid = n_grid
        self.levels = levels
        self.pre_smooth = pre_smooth
        self.post_smooth = post_smooth
        self.h = 1.0 / n_grid
        
    def _gauss_seidel_step(self, u, f, h):
        """Gauss-Seidel 平滑步骤（周期边界）"""
        n = u.shape[0]
        h2 = h ** 2
        u_new = u.clone()
        
        for i in range(n):
            for j in range(n):
                # 周期边界的邻居索引
                im1, ip1 = (i-1) % n, (i+1) % n
                jm1, jp1 = (j-1) % n, (j+1) % n
                
                # 五点差分格式
                u_new[i, j] = 0.25 * (
                    u[im1, j] + u[ip1, j] + u[i, jm1] + u[i, jp1] + h2 * f[i, j]
                )
        return u_new
    
    def _restrict(self, u):
        """限制算子：细网格 -> 粗网格"""
        n = u.shape[0]
        n_coarse = n // 2
        u_coarse = torch.zeros(n_coarse, n_coarse)
        
        for i in range(n_coarse):
            for j in range(n_coarse):
                # 简单的注入限制
                u_coarse[i, j] = u[2*i, 2*j]
                
        return u_coarse
    
    def _prolongate(self, u_coarse):
        """延拓算子：粗网格 -> 细网格"""
        n_coarse = u_coarse.shape[0]
        n_fine = n_coarse * 2
        u_fine = torch.zeros(n_fine, n_fine)
        
        for i in range(n_coarse):
            for j in range(n_coarse):
                # 双线性插值
                u_fine[2*i, 2*j] = u_coarse[i, j]
                u_fine[2*i+1, 2*j] = u_coarse[i, j]
                u_fine[2*i, 2*j+1] = u_coarse[i, j]
                u_fine[2*i+1, 2*j+1] = u_coarse[i, j]
                
        return u_fine
    
    def _compute_residual(self, u, f, h):
        """计算残差 r = f - Au"""
        n = u.shape[0]
        h2 = h ** 2
        r = torch.zeros_like(u)
        
        for i in range(n):
            for j in range(n):
                im1, ip1 = (i-1) % n, (i+1) % n
                jm1, jp1 = (j-1) % n, (j+1) % n
                
                laplacian_u = (u[im1, j] + u[ip1, j] + u[i, jm1] + u[i, jp1] - 4*u[i, j]) / h2
                r[i, j] = f[i, j] - laplacian_u
                
        return r
    
    def _v_cycle(self, u, f, h, level):
        """V-cycle 递归"""
        if level == 0 or u.shape[0] <= 4:
            # 最粗层：直接平滑
            for _ in range(10):
                u = self._gauss_seidel_step(u, f, h)
            return u
        
        # 前平滑
        for _ in range(self.pre_smooth):
            u = self._gauss_seidel_step(u, f, h)
            
        # 计算残差并限制到粗网格
        r = self._compute_residual(u, f, h)
        r_coarse = self._restrict(r)
        
        # 粗网格求解误差方程
        e_coarse = torch.zeros_like(r_coarse)
        e_coarse = self._v_cycle(e_coarse, r_coarse, h*2, level-1)
        
        # 延拓误差并校正
        e_fine = self._prolongate(e_coarse)
        u = u + e_fine
        
        # 后平滑
        for _ in range(self.post_smooth):
            u = self._gauss_seidel_step(u, f, h)
            
        return u
    
    def solve(self, f, max_iter=10):
        """
        多重网格求解
        f: [batch_size, 1, n, n] 或 [n, n]
        """
        original_shape = f.shape
        if f.dim() == 2:
            f = f.unsqueeze(0).unsqueeze(0)
        elif f.dim() == 3:
            f = f.unsqueeze(1)
            
        B, C, H, W = f.shape
        solutions = []
        
        for b in range(B):
            f_single = f[b, 0]
            u = torch.zeros_like(f_single)
            
            # V-cycle 迭代
            for _ in range(max_iter):
                u = self._v_cycle(u, f_single, self.h, self.levels)
                
            # 零均值约束
            u = u - u.mean()
            solutions.append(u)
            
        result = torch.stack(solutions).unsqueeze(1)
        
        # 恢复原始形状
        if len(original_shape) == 2:
            result = result.squeeze(0).squeeze(0)
        elif len(original_shape) == 3:
            result = result.squeeze(1)
            
        return result

class SimpleUNet(nn.Module):
    """纯数据驱动的 UNet 基线（无物理约束）"""
    def __init__(self, in_channels=1, out_channels=1):
        super(SimpleUNet, self).__init__()
        self.conv1 = nn.Conv2d(in_channels, 64, 3, padding=1, padding_mode='circular')
        self.conv2 = nn.Conv2d(64, 64, 3, padding=1, padding_mode='circular')
        self.down = nn.MaxPool2d(2)
        self.mid_conv1 = nn.Conv2d(64, 128, 3, padding=1, padding_mode='circular')
        self.mid_conv2 = nn.Conv2d(128, 128, 3, padding=1, padding_mode='circular')
        self.up = nn.ConvTranspose2d(128, 64, 2, stride=2)
        self.final_conv1 = nn.Conv2d(128, 64, 3, padding=1, padding_mode='circular')
        self.final_conv2 = nn.Conv2d(64, out_channels, 3, padding=1, padding_mode='circular')
        self.act = nn.SiLU()

    def forward(self, f):
        c1 = self.act(self.conv1(f))
        c2 = self.act(self.conv2(c1))
        d = self.down(c2)
        m1 = self.act(self.mid_conv1(d))
        m2 = self.act(self.mid_conv2(m1))
        u = self.up(m2)
        cat = torch.cat([u, c2], dim=1)
        f1 = self.act(self.final_conv1(cat))
        out = self.final_conv2(f1)
        return out

def benchmark_solvers(test_cases, n_grid=32, device='cuda'):
    """
    对比不同求解器的性能
    test_cases: list of (f, u_true) pairs
    """
    print(f"Benchmarking on {len(test_cases)} test cases, grid size: {n_grid}x{n_grid}")
    print("="*80)
    
    # 初始化求解器
    fft_solver = FFTSolver(n_grid)
    fd_solver = FiniteDifferenceSolver(n_grid)
    mg_solver = MultigridSolver(n_grid)
    
    results = {
        'FFT': {'times': [], 'errors': []},
        'Finite Difference': {'times': [], 'errors': []},
        'Multigrid': {'times': [], 'errors': []}
    }
    
    for i, (f, u_true) in enumerate(test_cases):
        f = f.to(device)
        u_true = u_true.to(device)
        
        # FFT 求解器
        start_time = time.time()
        u_fft = fft_solver.solve(f)
        fft_time = time.time() - start_time
        fft_error = torch.mean((u_fft - u_true) ** 2).item()
        results['FFT']['times'].append(fft_time)
        results['FFT']['errors'].append(fft_error)
        
        # 有限差分求解器
        start_time = time.time()
        u_fd = fd_solver.solve(f)
        fd_time = time.time() - start_time
        fd_error = torch.mean((u_fd.to(device) - u_true) ** 2).item()
        results['Finite Difference']['times'].append(fd_time)
        results['Finite Difference']['errors'].append(fd_error)
        
        # 多重网格求解器
        start_time = time.time()
        u_mg = mg_solver.solve(f)
        mg_time = time.time() - start_time
        mg_error = torch.mean((u_mg.to(device) - u_true) ** 2).item()
        results['Multigrid']['times'].append(mg_time)
        results['Multigrid']['errors'].append(mg_error)
        
        if i == 0:  # 打印第一个案例的详细结果
            print(f"Test case {i+1}:")
            print(f"  FFT:        Time: {fft_time:.4f}s, MSE: {fft_error:.2e}")
            print(f"  Finite Diff: Time: {fd_time:.4f}s, MSE: {fd_error:.2e}")
            print(f"  Multigrid:   Time: {mg_time:.4f}s, MSE: {mg_error:.2e}")
            print()
    
    # 统计结果
    print("Summary (mean ± std):")
    print("-" * 50)
    for method, data in results.items():
        avg_time = np.mean(data['times'])
        std_time = np.std(data['times'])
        avg_error = np.mean(data['errors'])
        std_error = np.std(data['errors'])
        print(f"{method:15s}: Time: {avg_time:.4f}±{std_time:.4f}s, MSE: {avg_error:.2e}±{std_error:.2e}")
    
    return results

if __name__ == "__main__":
    # 生成测试案例
    from pinn_mean_flow import PoissonDataset
    
    dataset = PoissonDataset(size=10, n=32)
    test_cases = []
    for i in range(10):
        f, u = dataset[i]
        test_cases.append((f.unsqueeze(0).unsqueeze(0), u.unsqueeze(0).unsqueeze(0)))
    
    # 运行基准测试
    results = benchmark_solvers(test_cases, n_grid=32)
